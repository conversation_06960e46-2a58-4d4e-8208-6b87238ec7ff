import { Schema, model } from 'mongoose';

const monitorSchema = new Schema({
    name: {
        type: String,
        required: true,
    },
    url: {
        type: String,
        required: true,
    },
    interval: {
        type: Number,
        required: true,
    },
    status: {
        type: String,
        enum: ['up', 'down'],
        default: 'up',
    },
    lastChecked: {
        type: Date,
        default: Date.now,
    },
    createdAt: {
        type: Date,
        default: Date.now,
    },
    updatedAt: {
        type: Date,
        default: Date.now,
    },
});

const Monitor = model('Monitor', monitorSchema);

export default Monitor;