import { Request, Response } from 'express';
import { ReportService } from '../services/reportService';

export class DashboardController {
    private reportService: ReportService;

    constructor() {
        this.reportService = new ReportService();
    }

    public async getDashboardData(req: Request, res: Response): Promise<void> {
        try {
            const data = await this.reportService.getDashboardReports();
            res.status(200).json(data);
        } catch (error) {
            res.status(500).json({ message: 'Error retrieving dashboard data', error });
        }
    }

    public async getServiceStatus(req: Request, res: Response): Promise<void> {
        try {
            const status = await this.reportService.getServiceStatus();
            res.status(200).json(status);
        } catch (error) {
            res.status(500).json({ message: 'Error retrieving service status', error });
        }
    }
}