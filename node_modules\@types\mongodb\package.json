{"name": "@types/mongodb", "version": "3.6.20", "description": "TypeScript definitions for MongoDB", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mongodb", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/CaselIT", "githubUsername": "CaselIT"}, {"name": "<PERSON>", "url": "https://github.com/alanmarcell", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/dante-101", "githubUsername": "dante-101"}, {"name": "<PERSON>", "url": "https://github.com/mcortesi", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/EnricoP<PERSON>ci", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/AJCStriker", "githubUsername": "AJCStriker"}, {"name": "<PERSON>", "url": "https://github.com/julien-c", "githubUsername": "julien-c"}, {"name": "<PERSON>", "url": "https://github.com/daprahamian", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/denys-bushulyak", "githubUsername": "denys-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/b4nst", "githubUsername": "b4nst"}, {"name": "<PERSON>", "url": "https://github.com/sindbach", "githubUsername": "sindbach"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/geral<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/various89", "githubUsername": "various89"}, {"name": "Angela-1", "url": "https://github.com/angela-1", "githubUsername": "angela-1"}, {"name": "<PERSON>", "url": "https://github.com/hector7", "githubUsername": "hector7"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/floric", "githubUsername": "floric"}, {"name": "<PERSON>", "url": "https://github.com/erikc5000", "githubUsername": "erikc5000"}, {"name": "<PERSON>", "url": "https://github.com/Manc", "githubUsername": "Manc"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/jloveridge", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ranguna", "githubUsername": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/HosseinAgha", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/albertossilva", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}, {"name": "<PERSON>", "url": "https://github.com/taxilian", "githubUsername": "taxilian"}, {"name": "<PERSON>", "url": "https://github.com/xamgore", "githubUsername": "xam<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/avaly", "githubUsername": "avaly"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/HitkoDev", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "TJT", "url": "https://github.com/Celend", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jtassin", "githubUsername": "j<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/addaleax", "githubUsername": "addaleax"}, {"name": "<PERSON>", "url": "https://github.com/emmanuel<PERSON>tier", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wyattjoh", "githubUsername": "wya<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/borfig", "githubUsername": "borfig"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mongodb"}, "scripts": {}, "dependencies": {"@types/bson": "*", "@types/node": "*"}, "typesPublisherContentHash": "4424cca7a452613f996286310bde1bc954ab161915fd1ab3c77f71592d73bb34", "typeScriptVersion": "3.6"}