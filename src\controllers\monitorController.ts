import { Request, Response } from 'express';
import { MonitorService } from '../services/monitorService';

export class MonitorController {
    private monitorService: MonitorService;

    constructor() {
        this.monitorService = new MonitorService();
    }

    public async addMonitor(req: Request, res: Response): Promise<void> {
        try {
            const monitorData = req.body;
            const newMonitor = await this.monitorService.createMonitor(monitorData);
            res.status(201).json(newMonitor);
        } catch (error) {
            res.status(500).json({ message: 'Error adding monitor', error });
        }
    }

    public async updateMonitor(req: Request, res: Response): Promise<void> {
        try {
            const monitorId = req.params.id;
            const monitorData = req.body;
            const updatedMonitor = await this.monitorService.updateMonitor(monitorId, monitorData);
            res.status(200).json(updatedMonitor);
        } catch (error) {
            res.status(500).json({ message: 'Error updating monitor', error });
        }
    }

    public async deleteMonitor(req: Request, res: Response): Promise<void> {
        try {
            const monitorId = req.params.id;
            await this.monitorService.deleteMonitor(monitorId);
            res.status(204).send();
        } catch (error) {
            res.status(500).json({ message: 'Error deleting monitor', error });
        }
    }

    public async getMonitors(req: Request, res: Response): Promise<void> {
        try {
            const monitors = await this.monitorService.getAllMonitors();
            res.status(200).json(monitors);
        } catch (error) {
            res.status(500).json({ message: 'Error fetching monitors', error });
        }
    }
}