import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
    },
});

export const sendEmailNotification = (to: string, subject: string, text: string) => {
    const mailOptions = {
        from: process.env.EMAIL_USER,
        to,
        subject,
        text,
    };

    transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
            console.error('Error sending email:', error);
        } else {
            console.log('Email sent:', info.response);
        }
    });
};

export const sendSMSNotification = (phoneNumber: string, message: string) => {
    // Implementation for sending SMS notifications
    console.log(`Sending SMS to ${phoneNumber}: ${message}`);
};

export const sendPushNotification = (deviceToken: string, message: string) => {
    // Implementation for sending push notifications
    console.log(`Sending push notification to ${deviceToken}: ${message}`);
};