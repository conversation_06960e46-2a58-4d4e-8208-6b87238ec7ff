import dotenv from 'dotenv';

dotenv.config();

const config = {
  port: process.env.PORT || 3000,
  db: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'user',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'uptime_monitoring',
  },
  monitoring: {
    checkInterval: process.env.CHECK_INTERVAL || 60000, // 60 seconds
    timeout: process.env.TIMEOUT || 5000, // 5 seconds
  },
  alerting: {
    emailService: process.env.EMAIL_SERVICE || 'gmail',
    emailUser: process.env.EMAIL_USER || '<EMAIL>',
    emailPassword: process.env.EMAIL_PASSWORD || 'password',
  },
};

export default config;