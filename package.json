{"name": "uptime-monitoring-app", "version": "1.0.0", "description": "A comprehensive uptime monitoring application that tracks the availability of websites, servers, and services.", "main": "src/app.ts", "scripts": {"start": "ts-node src/app.ts", "build": "tsc", "test": "jest"}, "dependencies": {"express": "^4.17.1", "mongoose": "^5.10.9", "dotenv": "^8.2.0", "nodemailer": "^6.4.11"}, "devDependencies": {"typescript": "^4.1.2", "ts-node": "^9.1.1", "jest": "^26.6.3", "@types/jest": "^26.0.20", "@types/node": "^14.14.31", "@types/express": "^4.17.8"}, "keywords": ["uptime", "monitoring", "web", "services", "availability"], "author": "Your Name", "license": "MIT"}