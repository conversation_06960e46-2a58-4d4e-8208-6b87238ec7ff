{"name": "uptime-monitoring-app", "version": "1.0.0", "description": "A comprehensive uptime monitoring application that tracks the availability of websites, servers, and services.", "main": "src/app.ts", "scripts": {"start": "ts-node src/app.ts", "build": "tsc", "test": "jest"}, "dependencies": {"dotenv": "^8.2.0", "express": "^4.17.1", "mongoose": "^8.15.1", "nodemailer": "^6.4.11"}, "devDependencies": {"@types/express": "^4.17.8", "@types/jest": "^29.5.14", "@types/node": "^14.14.31", "jest": "^29.7.0", "ts-node": "^9.1.1", "typescript": "^4.1.2"}, "keywords": ["uptime", "monitoring", "web", "services", "availability"], "author": "Your Name", "license": "MIT"}