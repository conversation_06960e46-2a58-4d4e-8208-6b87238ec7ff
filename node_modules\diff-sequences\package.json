{"name": "diff-sequences", "version": "26.6.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/diff-sequences"}, "license": "MIT", "description": "Compare items in two sequences to find a longest common subsequence", "keywords": ["fast", "linear", "space", "callback", "diff"], "engines": {"node": ">= 10.14.2"}, "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"perf": "node --expose-gc perf/index.js"}, "devDependencies": {"benchmark": "^2.1.4", "diff": "^4.0.1", "fast-check": "^2.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5"}