import express from 'express';
import bodyParser from 'body-parser';
import { json, urlencoded } from 'body-parser';
import { alertRoutes } from './routes/alertRoutes';
import { dashboardRoutes } from './routes/dashboardRoutes';
import { monitorRoutes } from './routes/monitorRoutes';
import { userRoutes } from './routes/userRoutes';
import { errorMiddleware } from './middlewares/errorMiddleware';
import { authMiddleware } from './middlewares/authMiddleware';
import { config } from './config/index';

const app = express();
const PORT = config.port || 3000;

app.use(json());
app.use(urlencoded({ extended: true }));

app.use(authMiddleware);

app.use('/api/alerts', alertRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/monitors', monitorRoutes);
app.use('/api/users', userRoutes);

app.use(errorMiddleware);

app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});