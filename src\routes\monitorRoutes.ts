import { Router } from 'express';
import MonitorController from '../controllers/monitorController';

const router = Router();
const monitorController = new MonitorController();

router.post('/', monitorController.createMonitor);
router.get('/', monitorController.getMonitors);
router.get('/:id', monitorController.getMonitorById);
router.put('/:id', monitorController.updateMonitor);
router.delete('/:id', monitorController.deleteMonitor);

export default router;