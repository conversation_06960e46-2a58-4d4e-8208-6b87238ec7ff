import { Report } from '../models/report';

export class ReportService {
    private reports: Report[] = [];

    public generateReport(monitorId: string, startDate: Date, endDate: Date): Report {
        const report: Report = {
            id: this.generateId(),
            monitorId,
            startDate,
            endDate,
            status: this.getStatus(monitorId, startDate, endDate),
            createdAt: new Date(),
        };
        this.reports.push(report);
        return report;
    }

    public getReports(): Report[] {
        return this.reports;
    }

    private generateId(): string {
        return Math.random().toString(36).substr(2, 9);
    }

    private getStatus(monitorId: string, startDate: Date, endDate: Date): string {
        // Logic to determine the status of the monitor between the given dates
        return 'up'; // Placeholder for actual status logic
    }
}