import { User } from '../models/user';

export class UserService {
    private users: User[] = [];

    public register(user: User): User {
        this.users.push(user);
        return user;
    }

    public authenticate(email: string, password: string): User | null {
        const user = this.users.find(u => u.email === email && u.password === password);
        return user || null;
    }

    public getUserById(userId: string): User | undefined {
        return this.users.find(u => u.id === userId);
    }

    public updateUser(userId: string, updatedData: Partial<User>): User | null {
        const userIndex = this.users.findIndex(u => u.id === userId);
        if (userIndex === -1) return null;

        this.users[userIndex] = { ...this.users[userIndex], ...updatedData };
        return this.users[userIndex];
    }

    public deleteUser(userId: string): boolean {
        const userIndex = this.users.findIndex(u => u.id === userId);
        if (userIndex === -1) return false;

        this.users.splice(userIndex, 1);
        return true;
    }

    public getAllUsers(): User[] {
        return this.users;
    }
}