import { Alert } from '../models/alert';
import { Notifier } from '../utils/notifier';

export class AlertService {
    private alerts: Alert[] = [];

    public createAlert(alertData: Alert): Alert {
        const newAlert = { ...alertData, id: this.generateId() };
        this.alerts.push(newAlert);
        Notifier.notify(`Alert created: ${newAlert.id}`);
        return newAlert;
    }

    public updateAlert(id: string, updatedData: Partial<Alert>): Alert | null {
        const alertIndex = this.alerts.findIndex(alert => alert.id === id);
        if (alertIndex === -1) return null;

        this.alerts[alertIndex] = { ...this.alerts[alertIndex], ...updatedData };
        Notifier.notify(`Alert updated: ${id}`);
        return this.alerts[alertIndex];
    }

    public deleteAlert(id: string): boolean {
        const alertIndex = this.alerts.findIndex(alert => alert.id === id);
        if (alertIndex === -1) return false;

        this.alerts.splice(alertIndex, 1);
        Notifier.notify(`Alert deleted: ${id}`);
        return true;
    }

    public getAlerts(): Alert[] {
        return this.alerts;
    }

    private generateId(): string {
        return (Math.random() * 1e18).toString(36);
    }
}