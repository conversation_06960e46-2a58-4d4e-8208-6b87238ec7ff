import { Monitor } from '../models/monitor';
import { AlertService } from './alertService';

export class MonitorService {
    private monitors: Monitor[] = [];
    private alertService: AlertService;

    constructor() {
        this.alertService = new AlertService();
    }

    addMonitor(monitor: Monitor): void {
        this.monitors.push(monitor);
    }

    updateMonitor(id: string, updatedMonitor: Monitor): void {
        const index = this.monitors.findIndex(m => m.id === id);
        if (index !== -1) {
            this.monitors[index] = { ...this.monitors[index], ...updatedMonitor };
        }
    }

    deleteMonitor(id: string): void {
        this.monitors = this.monitors.filter(m => m.id !== id);
    }

    getMonitors(): Monitor[] {
        return this.monitors;
    }

    checkMonitorAvailability(monitor: Monitor): boolean {
        // Logic to check the availability of the monitor
        // This could involve making an HTTP request or pinging a server
        return true; // Placeholder return value
    }

    notifyIfDown(monitor: Monitor): void {
        if (!this.checkMonitorAvailability(monitor)) {
            this.alertService.sendAlert(`Monitor ${monitor.name} is down!`);
        }
    }
}