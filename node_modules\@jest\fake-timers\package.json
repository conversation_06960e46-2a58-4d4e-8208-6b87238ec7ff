{"name": "@jest/fake-timers", "version": "26.6.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-fake-timers"}, "license": "MIT", "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@jest/types": "^26.6.2", "@sinonjs/fake-timers": "^6.0.1", "@types/node": "*", "jest-message-util": "^26.6.2", "jest-mock": "^26.6.2", "jest-util": "^26.6.2"}, "devDependencies": {"@types/sinonjs__fake-timers": "^6.0.1", "jest-snapshot-serializer-raw": "^1.1.0"}, "engines": {"node": ">= 10.14.2"}, "publishConfig": {"access": "public"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5"}