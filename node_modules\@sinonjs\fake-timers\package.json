{"name": "@sinonjs/fake-timers", "description": "Fake JavaScript timers", "version": "6.0.1", "homepage": "http://github.com/sinonjs/fake-timers", "author": "<PERSON>", "repository": {"type": "git", "url": "http://github.com/sinonjs/fake-timers.git"}, "bugs": {"mail": "<EMAIL>", "url": "http://github.com/sinonjs/fake-timers/issues"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint .", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "test-headless": "mochify --no-detect-globals --timeout=10000", "test-check-coverage": "npm run test-coverage && nyc check-coverage", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test": "npm run lint && npm run test-node && npm run test-headless", "bundle": "browserify --no-detect-globals -s FakeTimers -o fake-timers.js src/fake-timers-src.js", "prepublishOnly": "npm run bundle", "preversion": "./scripts/preversion.sh", "version": "./scripts/version.sh", "postversion": "./scripts/postversion.sh"}, "lint-staged": {"*.js": "eslint"}, "files": ["src/", "fake-timers.js"], "devDependencies": {"@sinonjs/referee-sinon": "6.0.1", "browserify": "16.5.0", "eslint": "6.8.0", "eslint-config-prettier": "6.10.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-ie11": "1.0.0", "eslint-plugin-mocha": "6.2.2", "eslint-plugin-prettier": "3.1.1", "husky": "4.2.1", "jsdom": "15.1.1", "lint-staged": "10.0.7", "mocha": "7.0.1", "mochify": "6.6.0", "npm-run-all": "4.1.5", "nyc": "14.1.1", "prettier": "1.19.1"}, "eslintConfig": {"extends": "eslint-config-sinon", "plugins": ["ie11"], "rules": {"ie11/no-collection-args": ["error"], "ie11/no-for-in-const": ["error"], "ie11/no-loop-func": ["warn"], "ie11/no-weak-collections": ["error"]}}, "module": "./fake-timers.js", "main": "./src/fake-timers-src.js", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "husky": {"hooks": {"pre-commit": "run-p lint test-node"}}, "nyc": {"branches": 85, "lines": 92, "functions": 92, "statements": 92, "exclude": ["**/*-test.js", "coverage/**", "fake-timers.js"]}}