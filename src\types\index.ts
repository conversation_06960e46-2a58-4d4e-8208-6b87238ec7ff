export interface Monitor {
    id: string;
    name: string;
    url: string;
    interval: number; // in seconds
    status: 'up' | 'down';
    lastChecked: Date;
}

export interface Alert {
    id: string;
    monitorId: string;
    message: string;
    createdAt: Date;
}

export interface User {
    id: string;
    username: string;
    password: string;
    email: string;
    role: 'admin' | 'user';
}

export interface Report {
    id: string;
    monitorId: string;
    status: 'up' | 'down';
    timestamp: Date;
}

export interface DashboardData {
    totalMonitors: number;
    upMonitors: number;
    downMonitors: number;
    alerts: Alert[];
}